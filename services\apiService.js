import axios from 'axios';
import { API_CONFIG, API_ENDPOINTS } from '../config/api';

/**
 * API Service for handling all API calls
 * Provides centralized methods for interacting with the backend
 */
class ApiService {
  constructor() {
    // Configure axios with base URL
    this.axiosInstance = axios.create({
      baseURL: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor to include auth token
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Add response interceptor for error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          // Token expired, clear auth data
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          delete axios.defaults.headers.common['Authorization'];
          // Redirect to auth page
          window.location.href = '/auth';
        } else if (error.response?.status === 402) {
          // Payment required - insufficient token balance
          console.error('Insufficient token balance for API request');
          // You can add custom handling here, like showing a modal or notification
        }
        return Promise.reject(error);
      }
    );
  }
  
  // ==================== GATEWAY INFO ====================
  
  /**
   * Get API Gateway information
   */
  async getGatewayInfo() {
    const response = await this.axiosInstance.get(API_ENDPOINTS.GATEWAY_INFO);
    return response.data;
  }

  // ==================== AUTHENTICATION ====================

  /**
   * Register new user
   * @param {Object} userData - User registration data
   * @param {string} userData.email - User email
   * @param {string} userData.password - User password
   */
  async register(userData) {
    const response = await this.axiosInstance.post(API_ENDPOINTS.AUTH.REGISTER, userData);
    return response.data;
  }

  /**
   * Login user
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   */
  async login(credentials) {
    const response = await this.axiosInstance.post(API_ENDPOINTS.AUTH.LOGIN, credentials);
    return response.data;
  }

  /**
   * Get user profile
   */
  async getProfile() {
    const response = await this.axiosInstance.get(API_ENDPOINTS.AUTH.PROFILE);
    return response.data;
  }

  /**
   * Update user profile
   * @param {Object} profileData - Profile update data
   */
  async updateProfile(profileData) {
    const response = await this.axiosInstance.put(API_ENDPOINTS.AUTH.UPDATE_PROFILE, profileData);
    return response.data;
  }

  /**
   * Change user password
   * @param {Object} passwordData - Password change data
   * @param {string} passwordData.currentPassword - Current password
   * @param {string} passwordData.newPassword - New password
   */
  async changePassword(passwordData) {
    const response = await this.axiosInstance.post(API_ENDPOINTS.AUTH.CHANGE_PASSWORD, passwordData);
    return response.data;
  }

  /**
   * Logout user
   */
  async logout() {
    const response = await this.axiosInstance.post(API_ENDPOINTS.AUTH.LOGOUT);
    return response.data;
  }

  /**
   * Get user token balance
   */
  async getTokenBalance() {
    const response = await this.axiosInstance.get(API_ENDPOINTS.AUTH.TOKEN_BALANCE);
    return response.data;
  }

  // ==================== ASSESSMENTS ====================

  /**
   * Submit assessment data for AI analysis
   * @param {Object} assessmentData - Assessment data
   * @param {Object} assessmentData.riasec - RIASEC scores
   * @param {Object} assessmentData.ocean - Big Five (OCEAN) scores
   * @param {Object} assessmentData.viaIs - VIA Character Strengths scores
   */
  async submitAssessment(assessmentData) {
    const response = await this.axiosInstance.post(API_ENDPOINTS.ASSESSMENT.SUBMIT, assessmentData);
    return response.data;
  }

  /**
   * Check assessment processing status
   * @param {string} jobId - Assessment job ID
   */
  async getAssessmentStatus(jobId) {
    const response = await this.axiosInstance.get(API_ENDPOINTS.ASSESSMENT.STATUS(jobId));
    return response.data;
  }

  // ==================== CHAT ====================

  /**
   * Start a new chat conversation with assessment context
   * @param {Object} data - Conversation data
   * @param {string} data.resultId - Assessment result ID
   * @param {Object} data.assessmentContext - Assessment result context
   */
  async startChatConversation(data) {
    const response = await this.axiosInstance.post(API_ENDPOINTS.CHAT.START_CONVERSATION, data);
    return response.data;
  }

  /**
   * Send a message in chat conversation
   * @param {Object} data - Message data
   * @param {string} data.conversationId - Conversation ID
   * @param {string} data.resultId - Assessment result ID
   * @param {string} data.message - User message
   */
  async sendChatMessage(data) {
    const response = await this.axiosInstance.post(API_ENDPOINTS.CHAT.SEND_MESSAGE, data);
    return response.data;
  }

  /**
   * Get chat conversation by result ID
   * @param {string} resultId - Assessment result ID
   */
  async getChatConversation(resultId) {
    const response = await this.axiosInstance.get(API_ENDPOINTS.CHAT.GET_CONVERSATION(resultId));
    return response.data;
  }

  // ==================== ARCHIVE ====================
  
  /**
   * Get user's analysis results with pagination
   * @param {Object} params - Query parameters
   * @param {number} params.page - Page number
   * @param {number} params.limit - Items per page
   * @param {string} params.status - Filter by status
   * @param {string} params.jobId - Filter by job ID
   */
  async getResults(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append('page', params.page);
    if (params.limit) queryParams.append('limit', params.limit);
    if (params.status) queryParams.append('status', params.status);
    if (params.jobId) queryParams.append('jobId', params.jobId);

    const url = `${API_ENDPOINTS.ARCHIVE.RESULTS}${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await this.axiosInstance.get(url);
    return response.data;
  }

  /**
   * Get specific analysis result
   * @param {string} resultId - Result ID
   */
  async getResultById(resultId) {
    const response = await this.axiosInstance.get(API_ENDPOINTS.ARCHIVE.RESULT_BY_ID(resultId));
    return response.data;
  }

  /**
   * Update analysis result
   * @param {string} resultId - Result ID
   * @param {Object} updateData - Update data
   */
  async updateResult(resultId, updateData) {
    const response = await this.axiosInstance.put(API_ENDPOINTS.ARCHIVE.UPDATE_RESULT(resultId), updateData);
    return response.data;
  }

  /**
   * Delete analysis result
   * @param {string} resultId - Result ID
   */
  async deleteResult(resultId) {
    const response = await this.axiosInstance.delete(API_ENDPOINTS.ARCHIVE.DELETE_RESULT(resultId));
    return response.data;
  }

  /**
   * Get user statistics
   */
  async getStats() {
    const response = await this.axiosInstance.get(API_ENDPOINTS.ARCHIVE.STATS);
    return response.data;
  }

  /**
   * Get user overview statistics
   */
  async getStatsOverview() {
    const response = await this.axiosInstance.get(API_ENDPOINTS.ARCHIVE.STATS_OVERVIEW);
    return response.data;
  }

  // ==================== HEALTH CHECK ====================

  /**
   * Comprehensive health check of all services
   */
  async getHealthStatus() {
    const response = await this.axiosInstance.get(API_ENDPOINTS.HEALTH.MAIN);
    return response.data;
  }

  /**
   * Simple liveness probe
   */
  async getLivenessStatus() {
    const response = await this.axiosInstance.get(API_ENDPOINTS.HEALTH.LIVE);
    return response.data;
  }

  /**
   * Readiness probe
   */
  async getReadinessStatus() {
    const response = await this.axiosInstance.get(API_ENDPOINTS.HEALTH.READY);
    return response.data;
  }

  /**
   * Extended health information
   */
  async getDetailedHealthStatus() {
    const response = await this.axiosInstance.get(API_ENDPOINTS.HEALTH.DETAILED);
    return response.data;
  }
}

// Create and export singleton instance
const apiService = new ApiService();
export default apiService;
